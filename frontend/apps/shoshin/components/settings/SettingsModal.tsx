"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>, Monitor, Sun, Moon } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useTheme, type Theme } from "@/stores/themeStore"
import { cn } from "@/lib/utils"

interface SettingsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

const themeOptions = [
  {
    value: 'light' as Theme,
    label: 'Light',
    icon: Sun,
    description: 'Light theme'
  },
  {
    value: 'dark' as Theme,
    label: 'Dark',
    icon: Moon,
    description: 'Dark theme'
  },
  {
    value: 'system' as Theme,
    label: 'System',
    icon: Monitor,
    description: 'Use system preference'
  }
]

export function SettingsModal({ open, onOpenChange }: SettingsModalProps) {
  const { theme, setTheme } = useTheme()
  const [activeSection, setActiveSection] = useState('general')

  const handleThemeChange = (newTheme: Theme) => {
    setTheme(newTheme)
  }

  const getCurrentThemeOption = () => {
    return themeOptions.find(option => option.value === theme)
  }

  if (!open) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/80"
        onClick={() => onOpenChange(false)}
      />

      {/* Modal */}
      <div className="relative bg-background border border-border rounded-lg shadow-lg w-full max-w-4xl h-[600px] mx-4 flex flex-col">
        {/* Header */}
        <div className="px-6 py-4 border-b border-border">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Settings
            </h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onOpenChange(false)}
              className="h-8 w-8 p-0"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* Sidebar Navigation */}
          <div className="w-64 border-r border-border bg-muted/30">
            <div className="p-4">
              <nav className="space-y-1">
                <button
                  onClick={() => setActiveSection('general')}
                  className={cn(
                    "w-full flex items-center gap-3 px-3 py-2 text-sm rounded-md transition-colors text-left",
                    activeSection === 'general'
                      ? "bg-primary text-primary-foreground"
                      : "text-muted-foreground hover:text-foreground hover:bg-muted"
                  )}
                >
                  <Settings className="w-4 h-4" />
                  General
                </button>
              </nav>
            </div>
          </div>

          {/* Content Area */}
          <div className="flex-1 overflow-y-auto">
            {activeSection === 'general' && (
              <div className="p-6">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium mb-4">General Settings</h3>
                  </div>

                  {/* Theme Section */}
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="theme" className="text-sm font-medium">
                        Theme
                      </Label>
                      <p className="text-sm text-muted-foreground mt-1">
                        Choose your preferred theme appearance
                      </p>
                    </div>

                    <div className="space-y-3">
                      <Select value={theme} onValueChange={handleThemeChange}>
                        <SelectTrigger className="w-48">
                          <SelectValue>
                            <div className="flex items-center gap-2">
                              {getCurrentThemeOption()?.icon && (
                                <getCurrentThemeOption()!.icon className="w-4 h-4" />
                              )}
                              {getCurrentThemeOption()?.label}
                            </div>
                          </SelectValue>
                        </SelectTrigger>
                        <SelectContent>
                          {themeOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              <div className="flex items-center gap-2">
                                <option.icon className="w-4 h-4" />
                                <div>
                                  <div className="font-medium">{option.label}</div>
                                  <div className="text-xs text-muted-foreground">
                                    {option.description}
                                  </div>
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Theme Preview */}
                    <div className="mt-4 p-4 border border-border rounded-lg bg-card">
                      <div className="text-sm font-medium mb-2">Preview</div>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 rounded-full bg-primary"></div>
                          <span className="text-sm">Primary color</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 rounded-full bg-secondary"></div>
                          <span className="text-sm">Secondary color</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 rounded-full bg-muted"></div>
                          <span className="text-sm">Muted color</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
